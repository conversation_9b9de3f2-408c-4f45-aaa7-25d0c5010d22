<%-- Fixed globalHeader.jspf with JSTL taglibs but without problematic Java classes --%>
<%
    // Simplified version without Hibernate and User class dependencies
    // This allows the JSP to compile and run without the missing Java classes
    
    // Basic user session handling without custom classes
    String remoteUser = request.getRemoteUser();
    if (remoteUser != null) {
        // User is authenticated, but we skip the custom User object creation for now
        // This can be re-enabled once the Java classes are properly compiled
    }
    
    // Basic session validation
    Object userObj = request.getSession().getAttribute("User");
    if (userObj != null && remoteUser == null) {
        // Clear invalid session
        request.getSession().removeAttribute("User");
        response.sendError(HttpServletResponse.SC_FORBIDDEN);
        return;
    }
%>
<%-- JSTL and custom taglib declarations --%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<hw:locale/>
