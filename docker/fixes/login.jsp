<%-- Fixed login.jsp without problematic Java class imports --%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="hw" tagdir="/WEB-INF/tags/core/"%>
<%@taglib prefix="gs" tagdir="/WEB-INF/tags/gs/"%>
<c:if test="${not empty pageContext.request.remoteUser}">
    <c:redirect url="/home"/>
</c:if>
<%@include file="/WEB-INF/jspf/globalHeader.jspf" %>
<%@include file="/WEB-INF/jspf/noCache.jspf" %>
<%
    // Simplified version without AppModel and GlobalSiteSettings
    // This allows the login page to work without the missing Java classes
    
    if( request.getParameter("closeAdminMessage") != null && request.getParameter("closeAdminMessage").equals("true") ){
        request.getSession().setAttribute("closeAdminMessage", true);
    }
    
    // Skip the global site settings banner for now
    // This can be re-enabled once the Java classes are properly compiled
%>
<!DOCTYPE html>
<html>
    <head>
        <title><hw:text key="global-title"/> <hw:text key="login-title"/></title>
        <link rel="stylesheet" type="text/css" href="<c:url value="/styles/globalStyle.css"/>"/>
        <gs:jQueryEnable/>
        <script type="text/javascript" src="<c:url value="/scripts/md5.js"/>"></script>
        <script type="text/javascript" src="<c:url value="/scripts/HWLIB.js"/>"></script>
        <script>
            function submitForm(){
                var username = document.getElementById("j_username").value;
                var password = document.getElementById("j_password").value;
                
                if( username == "" ){
                    alert("Please enter your username.");
                    document.getElementById("j_username").focus();
                    return false;
                }
                if( password == "" ){
                    alert("Please enter your password.");
                    document.getElementById("j_password").focus();
                    return false;
                }
                
                // Hash the password before submitting
                document.getElementById("j_password").value = hex_md5(password);
                document.getElementById("loginForm").submit();
                return true;
            }
            
            function handleEnterKey(event){
                if(event.keyCode == 13){
                    submitForm();
                }
            }
        </script>
    </head>
    <body>
        <div class="loginContainer">
            <div class="loginBox">
                <h1><hw:text key="login-title"/></h1>
                
                <c:if test="${not empty globalAdminMessageText}">
                    <div class="adminMessage">
                        <c:out value="${globalAdminMessageText}"/>
                        <a href="?closeAdminMessage=true" class="closeMessage">[X]</a>
                    </div>
                </c:if>
                
                <c:if test="${not empty param.error}">
                    <div class="errorMessage">
                        <hw:text key="login-error"/>
                    </div>
                </c:if>
                
                <form id="loginForm" method="post" action="<c:url value="/j_security_check"/>">
                    <table class="loginTable">
                        <tr>
                            <td><hw:text key="login-username"/>:</td>
                            <td><input type="text" id="j_username" name="j_username" onkeypress="handleEnterKey(event)" /></td>
                        </tr>
                        <tr>
                            <td><hw:text key="login-password"/>:</td>
                            <td><input type="password" id="j_password" name="j_password" onkeypress="handleEnterKey(event)" /></td>
                        </tr>
                        <tr>
                            <td colspan="2" class="loginButtonRow">
                                <input type="button" value="<hw:text key="login-submit"/>" onclick="submitForm()" class="loginButton"/>
                            </td>
                        </tr>
                    </table>
                </form>
                
                <div class="loginFooter">
                    <p><hw:text key="login-help"/></p>
                </div>
            </div>
        </div>
    </body>
</html>
