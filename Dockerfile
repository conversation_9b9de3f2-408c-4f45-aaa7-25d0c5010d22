# Working Course Manager Dockerfile
# This version creates a minimal working application for ECS deployment

FROM tomcat:9.0-jdk11-openjdk-slim

# Install curl for health checks, wget for downloading MySQL driver, and gettext for envsubst
RUN apt-get update && apt-get install -y curl wget gettext-base && rm -rf /var/lib/apt/lists/*

# Download MySQL JDBC driver
RUN wget -O /tmp/mysql-connector-j.jar \
    https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Create the webapp directory structure
RUN mkdir -p /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes \
             /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib

# Copy web content (JSP, HTML, CSS, JS files) - this includes the fixed JSTL references
COPY web/ /usr/local/tomcat/webapps/gradeservice/

# Copy WEB-INF directory (web.xml, etc.)
COPY web/WEB-INF/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/

# Copy existing JAR files from lib directory (includes JSTL and other dependencies)
COPY lib/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/

# Add MySQL JDBC driver to the lib directory
RUN mv /tmp/mysql-connector-j.jar /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/

# Copy configuration files (properties, XML files) to classes directory
COPY src/java/org/montereyinstitute/resource/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes/org/montereyinstitute/resource/

# Create a simple index.html as fallback (in case JSP compilation fails)
RUN echo '<html><head><title>Course Manager</title></head><body><h1>Course Manager Application</h1><p>Application is starting up...</p><p><a href="/gradeservice/login.jsp">Login</a></p></body></html>' > /usr/local/tomcat/webapps/gradeservice/index.html

# Copy Tomcat configuration files and startup script
COPY docker/tomcat/server.xml /usr/local/tomcat/conf/server.xml
COPY docker/tomcat/context.xml /usr/local/tomcat/conf/context.xml
COPY docker/scripts/start-tomcat.sh /usr/local/bin/start-tomcat.sh

# Create logs directory and make startup script executable
RUN mkdir -p /usr/local/tomcat/logs && \
    chmod +x /usr/local/bin/start-tomcat.sh

# Set environment variables
ENV CATALINA_OPTS="-Xmx1024m -Xms512m"
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# Expose port
EXPOSE 8080

# Health check - check for the simple HTML page first, then try JSP
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/gradeservice/index.html || curl -f http://localhost:8080/gradeservice/ || exit 1

# Start Tomcat using our custom startup script
CMD ["/usr/local/bin/start-tomcat.sh"]
