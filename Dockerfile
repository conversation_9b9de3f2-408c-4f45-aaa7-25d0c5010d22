# Course Manager Dockerfile - Simple JSTL Fix
# This version fixes the JSTL taglib issue by copying fixed files

FROM tomcat:9.0-jdk11-openjdk-slim

# Install curl for health checks, wget for downloading dependencies, and gettext for envsubst
RUN apt-get update && apt-get install -y curl wget gettext-base && rm -rf /var/lib/apt/lists/*

# Download MySQL JDBC driver to Tomcat's global lib directory (required for JNDI datasources)
RUN wget -O /usr/local/tomcat/lib/mysql-connector-j.jar \
    https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Create the webapp directory structure
RUN mkdir -p /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes \
             /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib

# Copy web content (JSP, HTML, CSS, JS files)
COPY web/ /usr/local/tomcat/webapps/gradeservice/

# Copy WEB-INF directory (web.xml, etc.)
COPY web/WEB-INF/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/

# Copy existing JAR files from lib directory (includes JSTL and other dependencies)
COPY lib/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/

# Copy configuration files (properties, XML files) to classes directory
COPY src/java/org/montereyinstitute/resource/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes/org/montereyinstitute/resource/

# Copy fixed JSP files that include proper JSTL taglib declarations
COPY docker/fixes/globalHeader.jspf /usr/local/tomcat/webapps/gradeservice/WEB-INF/jspf/globalHeader.jspf
COPY docker/fixes/index.jsp /usr/local/tomcat/webapps/gradeservice/index.jsp

# Create a simple fallback HTML page
RUN echo '<!DOCTYPE html><html><head><title>Course Manager</title></head><body><h1>Course Manager Application</h1><p>Application is running...</p><p><a href="/gradeservice/login.jsp">Login</a></p></body></html>' > /usr/local/tomcat/webapps/gradeservice/index.html

# Fix permissions for Tomcat work directory (critical for JSP compilation in ECS)
RUN mkdir -p /usr/local/tomcat/work && \
    chmod -R 755 /usr/local/tomcat/work && \
    chown -R root:root /usr/local/tomcat/work

# Fix permissions for temp directory
RUN mkdir -p /usr/local/tomcat/temp && \
    chmod -R 755 /usr/local/tomcat/temp && \
    chown -R root:root /usr/local/tomcat/temp

# Fix permissions for logs directory
RUN mkdir -p /usr/local/tomcat/logs && \
    chmod -R 755 /usr/local/tomcat/logs && \
    chown -R root:root /usr/local/tomcat/logs

# Fix permissions for webapp directory
RUN chmod -R 755 /usr/local/tomcat/webapps/gradeservice && \
    chown -R root:root /usr/local/tomcat/webapps/gradeservice

# Copy Tomcat configuration files and startup script
COPY docker/tomcat/server.xml /usr/local/tomcat/conf/server.xml
COPY docker/tomcat/context.xml /usr/local/tomcat/conf/context.xml
COPY docker/scripts/start-tomcat.sh /usr/local/bin/start-tomcat.sh

# Make startup script executable
RUN chmod +x /usr/local/bin/start-tomcat.sh

# Set environment variables
ENV CATALINA_OPTS="-Xmx1024m -Xms512m"
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# Set JVM options for JSP compilation
ENV JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
ENV JAVA_OPTS="$JAVA_OPTS -Dorg.apache.jasper.compiler.disablejsr199=true"

# Expose port
EXPOSE 8080

# Health check - check for the simple HTML page first, then try JSP
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/gradeservice/index.html || curl -f http://localhost:8080/gradeservice/ || exit 1

# Start Tomcat using our custom startup script
CMD ["/usr/local/bin/start-tomcat.sh"]
