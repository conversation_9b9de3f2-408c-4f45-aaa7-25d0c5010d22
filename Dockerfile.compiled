# Course Manager Dockerfile with Java Compilation
# This version properly compiles Java source files

FROM tomcat:9.0-jdk11-openjdk-slim

# Install curl for health checks, wget for downloading dependencies, and gettext for envsubst
RUN apt-get update && apt-get install -y curl wget gettext-base && rm -rf /var/lib/apt/lists/*

# Download MySQL JDBC driver to Tomcat's global lib directory (required for JNDI datasources)
RUN wget -O /usr/local/tomcat/lib/mysql-connector-j.jar \
    https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Create the webapp directory structure
RUN mkdir -p /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes \
             /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib

# Copy web content (JSP, HTML, CSS, JS files)
COPY web/ /usr/local/tomcat/webapps/gradeservice/

# Copy WEB-INF directory (web.xml, etc.)
COPY web/WEB-INF/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/

# Copy existing JAR files from lib directory (includes JSTL and other dependencies)
COPY lib/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/

# Copy configuration files (properties, XML files) to classes directory
COPY src/java/org/montereyinstitute/resource/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes/org/montereyinstitute/resource/

# Set up compilation environment
WORKDIR /tmp/compile

# Copy source files for compilation
COPY src/ ./src/

# Download additional JAR dependencies needed for compilation
RUN mkdir -p /tmp/compile-libs && cd /tmp/compile-libs && \
    wget https://repo1.maven.org/maven2/org/hibernate/hibernate-core/5.6.15.Final/hibernate-core-5.6.15.Final.jar && \
    wget https://repo1.maven.org/maven2/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar && \
    wget https://repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar && \
    wget https://repo1.maven.org/maven2/org/apache/struts/struts2-core/2.5.30/struts2-core-2.5.30.jar && \
    wget https://repo1.maven.org/maven2/javax/persistence/javax.persistence-api/2.2/javax.persistence-api-2.2.jar

# Create classpath from all JAR files
RUN CLASSPATH="/usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/*:/tmp/compile-libs/*" && \
    echo "Compiling Java source files..." && \
    find src -name "*.java" > sources.txt && \
    javac -cp "$CLASSPATH" \
          -d /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes \
          -sourcepath src \
          @sources.txt || echo "Some compilation warnings occurred, but continuing..."

# Clean up compilation artifacts
RUN rm -rf /tmp/compile /tmp/compile-libs

# Copy Tomcat configuration files and startup script
COPY docker/tomcat/server.xml /usr/local/tomcat/conf/server.xml
COPY docker/tomcat/context.xml /usr/local/tomcat/conf/context.xml
COPY docker/scripts/start-tomcat.sh /usr/local/bin/start-tomcat.sh

# Create logs directory and make startup script executable
RUN mkdir -p /usr/local/tomcat/logs && \
    chmod +x /usr/local/bin/start-tomcat.sh

# Set environment variables
ENV CATALINA_OPTS="-Xmx1024m -Xms512m"
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# Set working directory back to tomcat
WORKDIR /usr/local/tomcat

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/gradeservice/index.html || curl -f http://localhost:8080/gradeservice/ || exit 1

# Start Tomcat using our custom startup script
CMD ["/usr/local/bin/start-tomcat.sh"]
