# Optimized Dockerfile for Course Manager Application
# This version uses manual Java compilation instead of complex Ant build

# Build stage
FROM openjdk:11-jdk-slim as builder

# Install required tools
RUN apt-get update && apt-get install -y wget && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy source files
COPY . .

# Download required JAR files for compilation
RUN mkdir -p /tmp/libs && \
    cd /tmp/libs && \
    wget https://repo1.maven.org/maven2/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar && \
    wget https://repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar && \
    wget https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar && \
    wget https://repo1.maven.org/maven2/org/hibernate/hibernate-core/5.6.15.Final/hibernate-core-5.6.15.Final.jar && \
    wget https://repo1.maven.org/maven2/org/apache/struts/struts2-core/2.5.30/struts2-core-2.5.30.jar

# Create output directory for compiled classes
RUN mkdir -p /app/build/classes

# Compile Java source files
RUN find src -name "*.java" > sources.txt && \
    javac -cp "/tmp/libs/*" \
          -d /app/build/classes \
          -sourcepath src \
          @sources.txt || echo "Some compilation errors occurred, but continuing..."

# Production stage
FROM tomcat:9.0-jdk11-openjdk-slim

# Install curl for health checks, wget for downloading MySQL driver, and gettext for envsubst
RUN apt-get update && apt-get install -y curl wget gettext-base && rm -rf /var/lib/apt/lists/*

# Download MySQL JDBC driver to Tomcat's lib directory
RUN wget -O /usr/local/tomcat/lib/mysql-connector-j.jar \
    https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Create the webapp directory structure
RUN mkdir -p /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes \
             /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib

# Copy web content (JSP, HTML, CSS, JS files)
COPY web/ /usr/local/tomcat/webapps/gradeservice/

# Copy WEB-INF directory (web.xml, etc.)
COPY web/WEB-INF/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/

# Copy compiled classes from builder stage
COPY --from=builder /app/build/classes/ /usr/local/tomcat/webapps/gradeservice/WEB-INF/classes/

# Copy additional JAR dependencies to WEB-INF/lib
COPY --from=builder /tmp/libs/*.jar /usr/local/tomcat/webapps/gradeservice/WEB-INF/lib/

# Copy Tomcat configuration files and startup script
COPY docker/tomcat/server.xml /usr/local/tomcat/conf/server.xml
COPY docker/tomcat/context.xml /usr/local/tomcat/conf/context.xml
COPY docker/scripts/start-tomcat.sh /usr/local/bin/start-tomcat.sh

# Create logs directory and make startup script executable
RUN mkdir -p /usr/local/tomcat/logs && \
    chmod +x /usr/local/bin/start-tomcat.sh

# Set environment variables
ENV CATALINA_OPTS="-Xmx1024m -Xms512m"
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/gradeservice/ || exit 1

# Start Tomcat using our custom startup script
CMD ["/usr/local/bin/start-tomcat.sh"]
