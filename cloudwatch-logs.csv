timestamp,message
1753231291805,23-Jul-2025 00:41:31.804 WARNING [http-nio-8080-exec-1] org.apache.catalina.authenticator.FormAuthenticator.forwardToLoginPage Unexpected error forwarding to login page
1753231291805,	org.apache.jasper.JasperException: Unable to compile class for JSP: 
1753231291805,An error occurred at line: [14] in the generated java file: [/usr/local/tomcat/work/Catalina/localhost/gradeservice/org/apache/jsp/WEB_002dINF/login_jsp.java]
1753231291805,Only a type can be imported. gs.hinkleworld.core.AppModel resolves to a package
1753231291805,An error occurred at line: [15] in the generated java file: [/usr/local/tomcat/work/Catalina/localhost/gradeservice/org/apache/jsp/WEB_002dINF/login_jsp.java]
1753231291805,Only a type can be imported. org.montereyinstitute.model.GlobalSiteSettings resolves to a package
1753231291805,An error occurred at line: [14] in the jsp file: [/WEB-INF/login.jsp]
1753231291805,GlobalSiteSettings cannot be resolved to a type
1753231291805,"11:         request.getSession().setAttribute(""closeAdminMessage"", true);"
1753231291805,12:     }
1753231291805,"13:     if( request.getSession().getAttribute(""closeAdminMessage"") == null && request.getAttribute(""adminMessageInPreviewMode"") == null ){"
1753231291805,14:         GlobalSiteSettings globalSettings = AppModel.getAppModel(request).getGlobalSettings();
1753231291805,15:         if( globalSettings != null ){
1753231291805,16:             if( globalSettings.getSiteBannerOn() && globalSettings.getSiteBannerText() != null && !globalSettings.getSiteBannerText().isEmpty() ){
1753231291805,"17:                 request.setAttribute(""globalAdminMessageText"", globalSettings.getSiteBannerText());"
1753231291805,An error occurred at line: [14] in the jsp file: [/WEB-INF/login.jsp]
1753231291805,AppModel cannot be resolved
1753231291805,"11:         request.getSession().setAttribute(""closeAdminMessage"", true);"
1753231291805,12:     }
1753231291805,"13:     if( request.getSession().getAttribute(""closeAdminMessage"") == null && request.getAttribute(""adminMessageInPreviewMode"") == null ){"
1753231291805,14:         GlobalSiteSettings globalSettings = AppModel.getAppModel(request).getGlobalSettings();
1753231291805,15:         if( globalSettings != null ){
1753231291805,16:             if( globalSettings.getSiteBannerOn() && globalSettings.getSiteBannerText() != null && !globalSettings.getSiteBannerText().isEmpty() ){
1753231291805,"17:                 request.setAttribute(""globalAdminMessageText"", globalSettings.getSiteBannerText());"
1753231291805,Stacktrace:
1753231291805,		at org.apache.jasper.compiler.DefaultErrorHandler.javacError(DefaultErrorHandler.java:102)
1753231291805,		at org.apache.jasper.compiler.ErrorDispatcher.javacError(ErrorDispatcher.java:213)
1753231291805,		at org.apache.jasper.compiler.JDTCompiler.generateClass(JDTCompiler.java:509)
1753231291805,		at org.apache.jasper.compiler.Compiler.compile(Compiler.java:397)
1753231291805,		at org.apache.jasper.compiler.Compiler.compile(Compiler.java:367)
1753231291805,		at org.apache.jasper.compiler.Compiler.compile(Compiler.java:351)
1753231291805,		at org.apache.jasper.JspCompilationContext.compile(JspCompilationContext.java:605)
1753231291805,		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:399)
1753231291805,		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
1753231291805,		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
1753231291805,		at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
1753231291805,		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
1753231291805,		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
1753231291805,		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
1753231291805,		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
1753231291805,		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
1753231291805,		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
1753231291805,		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
1753231291805,		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
1753231291805,		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
1753231291805,		at org.apache.catalina.authenticator.FormAuthenticator.forwardToLoginPage(FormAuthenticator.java:434)
1753231291805,		at org.apache.catalina.authenticator.FormAuthenticator.doAuthenticate(FormAuthenticator.java:228)
1753231291805,		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:625)
1753231291805,		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
1753231291805,		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
1753231291805,		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:687)
1753231291805,		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
1753231291805,		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
1753231291805,		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
1753231291805,		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
1753231291805,		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
1753231291805,		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
1753231291805,		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
1753231291805,		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
1753231291805,		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
1753231291805,		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
1753231291805,		at java.base/java.lang.Thread.run(Thread.java:829)