timestamp,message
1753075844760,🚀 Starting Course Manager Application...
1753075844761,📊 Environment: staging
1753075844761,🔍 Database Configuration:
1753075844761,   Host: staging-course-manager.cfyyzrxdmvqc.us-west-2.rds.amazonaws.com
1753075844761,   Port: 3306
1753075844761,   Database: gradeservice
1753075844761,   Username: gradeservice
1753075844761,   Password: [HIDDEN]
1753075844765,🔧 Configuring Tomcat server.xml with environment variables...
1753075844767,✅ Database URL configured as:
1753075844770,"url=""***********************************************************************************************************************************************************************************************"""
1753075844771,"ℹ️  MySQL client not available, skipping connectivity test"
1753075844771,🚀 Starting Tomcat...
1753075844799,NOTE: Picked up JDK_JAVA_OPTIONS:  --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
1753075847103,21-Jul-2025 05:30:47.096 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.65
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jul 14 2022 12:28:53 UTC
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: ********
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            5.10.238-231.953.amzn2.x86_64
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
1753075847104,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/local/openjdk-11
1753075847105,21-Jul-2025 05:30:47.104 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           11.0.16+8
1753075847105,21-Jul-2025 05:30:47.105 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
1753075847105,21-Jul-2025 05:30:47.105 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /usr/local/tomcat
1753075847105,21-Jul-2025 05:30:47.105 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /usr/local/tomcat
1753075847202,21-Jul-2025 05:30:47.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
1753075847202,21-Jul-2025 05:30:47.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
1753075847202,21-Jul-2025 05:30:47.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
1753075847202,21-Jul-2025 05:30:47.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
1753075847202,21-Jul-2025 05:30:47.202 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/usr/local/tomcat/conf/logging.properties
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.security.egd=file:/dev/./urandom
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
1753075847203,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
1753075847204,21-Jul-2025 05:30:47.203 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xmx1024m
1753075847204,21-Jul-2025 05:30:47.204 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xms512m
1753075847204,21-Jul-2025 05:30:47.204 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
1753075847204,21-Jul-2025 05:30:47.204 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/usr/local/tomcat
1753075847204,21-Jul-2025 05:30:47.204 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/usr/local/tomcat
1753075847204,21-Jul-2025 05:30:47.204 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/usr/local/tomcat/temp
1753075847213,21-Jul-2025 05:30:47.212 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
1753075847295,"21-Jul-2025 05:30:47.294 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true]."
1753075847295,"21-Jul-2025 05:30:47.295 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]"
1753075847312,21-Jul-2025 05:30:47.312 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 1.1.1n  15 Mar 2022]
1753075849097,"21-Jul-2025 05:30:49.097 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler [""http-nio-8080""]"
1753075849299,21-Jul-2025 05:30:49.299 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [3407] milliseconds
1753075851014,21-Jul-2025 05:30:51.014 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
1753075851090,21-Jul-2025 05:30:51.090 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.65]
1753075851111,21-Jul-2025 05:30:51.110 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/usr/local/tomcat/webapps/gradeservice]
1753075851390,21-Jul-2025 05:30:51.387 WARNING [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Context] failed to set property [antiJARLocking] to [true]
1753075851490,21-Jul-2025 05:30:51.490 WARNING [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Context] failed to set property [antiJARLocking] to [true]
1753075862663,21-Jul-2025 05:31:02.662 INFO [main] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
1753075863211,"21-Jul-2025 05:31:03.206 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/usr/local/tomcat/webapps/gradeservice] has finished in [12,103] ms"
1753075863228,"21-Jul-2025 05:31:03.227 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler [""http-nio-8080""]"
1753075863317,21-Jul-2025 05:31:03.317 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [14016] milliseconds
1753076235559,"21-Jul-2025 05:37:15.558 INFO [Thread-3] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler [""http-nio-8080""]"
1753076235563,21-Jul-2025 05:37:15.561 INFO [Thread-3] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
1753076235601,21-Jul-2025 05:37:15.600 WARNING [Thread-3] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads The web application [gradeservice] appears to have started a thread named [mysql-cj-abandoned-connection-cleanup] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
1753076235601, java.base@11.0.16/java.lang.Object.wait(Native Method)
1753076235601, java.base@11.0.16/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
1753076235601, com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:85)
1753076235601, java.base@11.0.16/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
1753076235601, java.base@11.0.16/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
1753076235601, java.base@11.0.16/java.lang.Thread.run(Thread.java:829)
1753076235620,"21-Jul-2025 05:37:15.620 INFO [Thread-3] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler [""http-nio-8080""]"
1753076235626,"21-Jul-2025 05:37:15.626 INFO [Thread-3] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler [""http-nio-8080""]"